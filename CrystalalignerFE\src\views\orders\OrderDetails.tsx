import OrderImageGrid from '@/components/page-components/Orders/OrderComponents/OrderImageGrid'
import OrderNoteContainer from '@/components/page-components/Orders/OrderComponents/OrderNoteContainer'
import OrderStatusContainer from '@/components/page-components/Orders/OrderComponents/OrderStatusContainer'
import TreatmentPlansContainer from '@/components/page-components/Orders/OrderComponents/TreatmentPlansContainer'
import PatientEditDrawer from '@/components/page-components/Orders/PatientEditDrawer'
import {
    ActionLink,
    ConfirmDialog,
    TextBlockSkeleton,
} from '@/components/shared'
import DetailShadowContainer from '@/components/shared/DetailShadowContainer'
import { Button, toast } from '@/components/ui'
import { CDN_URL } from '@/constants/api.constant'
import { dateLocales } from '@/locales'
import { apiDeleteOrder, apiGetOrderById } from '@/services/OrderService'
import { apiGetPatientById } from '@/services/PatientService'
import { useAppDispatch, useAppSelector } from '@/store'
import { setModalType } from '@/store/slices/modal'
import { useCallback, useEffect, useState } from 'react'
import {
    BsArrow90DegLeft,
    BsArrowUpRightSquare,
    BsDownload,
} from 'react-icons/bs'
import { FaCheck } from 'react-icons/fa'
import { HiTrash } from 'react-icons/hi'
import { Link, useNavigate, useParams } from 'react-router-dom'
import OrderPlanDetailContainer from './OrderPlanDetailContainer'

const OrderDetails = () => {
    const [order, setOrder] = useState<any>({})
    const [patient, setPatient] = useState<any>({})
    const refreshTable = useAppSelector((state) => state.table.refreshTable)
    const [loading, setLoading] = useState<boolean>(false)
    const param = useParams<{ id: string }>().id
    const orderId = parseInt(param || '0')
    const userType = useAppSelector((state) => state.auth.user.UserType)
    const dispatch = useAppDispatch()

    const getOrderInfo = useCallback(async () => {
        setLoading(true)
        const response = await apiGetOrderById(orderId)
        if ((response as any)?.data.Success == true) {
            setOrder((response as any)?.data.Data)
            console.log('order', (response as any)?.data.Data)
            setLoading(false)
        } else {
            setLoading(false)
            setOrder({})
        }
    }, [orderId, refreshTable])

    const getPatientInfo = async () => {
        if (order.PatientId == null) return
        setLoading(true)

        try {
            const response = await apiGetPatientById(order.PatientId)
            if ((response as any)?.data.Success == true) {
                setPatient((response as any)?.data.Data)
                setLoading(false)
            }
        } catch (error) {
            setLoading(false)
            setPatient({})
        }
    }

    useEffect(() => {
        getOrderInfo()
    }, [orderId, refreshTable])

    useEffect(() => {
        getPatientInfo()
    }, [order, refreshTable])

    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState<boolean>(false)

    return (
        <>
            {loading ? (
                <TextBlockSkeleton
                    animation
                    title
                    rowCount={15}
                    variant="block"
                />
            ) : (
                <div className="flex flex-col gap-4">
                    <div className="w-full flex flex-col gap-4 lg:gap-0 lg:flex-row justify-between lg:items-center">
                        <h2 className="">
                            Vaka Detay{' '}
                            <span className="font-light">
                                {'- '}
                                {patient?.Name || ''}
                            </span>
                        </h2>
                        <div className="flex gap-4">
                            {userType == 1 && (
                                <>
                                    <Button
                                        onClick={() => {
                                            setIsDeleteDialogOpen(true)
                                        }}
                                        variant="twoTone"
                                        size="md"
                                        color="red"
                                        className="flex gap-2 items-center"
                                    >
                                        <HiTrash size={20} className="mb-1" />
                                        Vakayı Sil
                                    </Button>
                                    <DeleteOrderDialog
                                        order={order}
                                        isDeleteDialogOpen={isDeleteDialogOpen}
                                        setIsDeleteDialogOpen={
                                            setIsDeleteDialogOpen
                                        }
                                    />
                                </>
                            )}
                            <Link
                                to={'/orders'}
                                className="flex items-center gap-2 rounded-lg px-4 py-2 border border-gray-400 max-w-fit bg-gray-500 text-white"
                            >
                                <BsArrow90DegLeft />
                                Geri Dön
                            </Link>
                        </div>
                    </div>
                    <h5 className=" text-sky-950 italic flex justify-between items-center border shadow-lg rounded-xl p-8">
                        <div className="flex flex-col">
                            <div className="font-normal flex gap-2">
                                Oluşturan -
                                <a
                                    href={`/doctors/${
                                        order?.CreatedId > 0
                                            ? order?.CreatedId
                                            : ''
                                    }`}
                                    className="underline not-italic cursor-pointer text-sky-500 flex items-center gap-1"
                                >
                                    {order?.CreatedBy}{' '}
                                    <BsArrowUpRightSquare size={16} />
                                </a>
                            </div>
                            <div className="font-light flex flex-col gap-2 ">
                                <div>
                                    {'Oluşturma Tarihi - '}
                                    {new Date(
                                        order?.CreatedDate
                                    ).toLocaleDateString('tr-tr', dateLocales)}
                                </div>
                                <div className=" text-sm">
                                    {new Date(
                                        order?.CreatedDate
                                    ).toLocaleTimeString('tr-tr')}
                                </div>
                                <div className="flex gap-1">
                                    <p className="font-semibold">
                                        Sipariş No:{' '}
                                    </p>
                                    <p className="font-semibold">{order?.Id}</p>
                                </div>
                            </div>
                        </div>
                        {/*    <div>
                            {order?.SpecialInstruction && (
                                <div>
                                    <Tooltip title={order?.SpecialInstruction}>
                                        <BsFillInfoCircleFill
                                            color="orange"
                                            className="animate-pulse"
                                            size={36}
                                        />
                                    </Tooltip>
                                </div>
                            )}
                        </div> */}
                    </h5>
                    <hr />
                    {order?.SpecialInstruction && (
                        <OrderNoteContainer note={order?.SpecialInstruction} />
                    )}
                    <hr />
                    <OrderStatusContainer order={order} />
                    <hr />
                    <PatientEditDrawer patient={patient} />
                    <DetailShadowContainer
                        header="Hasta Bilgileri"
                        button
                        buttonText="Düzenle"
                        buttonOnClick={() => {
                            dispatch(setModalType('editPatient'))
                        }}
                    >
                        <ul className="space-y-4 text-lg">
                            <li className="flex gap-4">
                                <h5>Hasta İsmi: </h5>
                                <span>{patient?.Name}</span>
                            </li>
                            <li className="flex gap-4">
                                <h5>Cinsiyet: </h5>
                                <span>
                                    {patient?.Gender == 0 ? 'Erkek' : 'Kadın'}
                                </span>
                            </li>
                            <li className="flex gap-4">
                                <h5>Doğum Tarihi: </h5>
                                <span>
                                    {new Date(
                                        patient?.BirthDate
                                    ).toLocaleDateString('tr-tr', {
                                        year: 'numeric',
                                        month: 'long',
                                        day: 'numeric',
                                    })}
                                </span>
                            </li>
                            <li className="flex gap-4">
                                <h5>TCKN: </h5>
                                <span>{patient?.Tckn}</span>
                            </li>
                        </ul>
                    </DetailShadowContainer>
                    <OrderPlanDetailContainer order={order} />
                    <hr />
                    <div className="space-y-4 border shadow-lg rounded-xl p-8">
                        <h2>Hasta Fotoğrafları</h2>
                        <OrderImageGrid order={order} loading={loading} />
                    </div>
                    <hr />
                    <div className="space-y-4 border shadow-lg rounded-xl p-8">
                        <h2>Impression / STL</h2>
                        {order?.StlBite ||
                        order?.StlLower ||
                        order?.StlUpper ? (
                            <div className="flex flex-col gap-4">
                                <ActionLink
                                    download={true}
                                    rel="noreferrer"
                                    target="_blank"
                                    href={CDN_URL + order?.StlBite}
                                    className="flex gap-1 items-center text-lg"
                                >
                                    <BsDownload />
                                    STL Kapanış
                                </ActionLink>
                                <ActionLink
                                    href={CDN_URL + order?.StlLower}
                                    download={true}
                                    rel="noreferrer"
                                    target="_blank"
                                    className="flex gap-1 items-center text-lg"
                                >
                                    <BsDownload />
                                    STL Alt Çene
                                </ActionLink>
                                <ActionLink
                                    href={CDN_URL + order?.StlUpper}
                                    download={true}
                                    rel="noreferrer"
                                    target="_blank"
                                    className="flex gap-1 items-center text-lg"
                                >
                                    <BsDownload />
                                    STL Üst Çene
                                </ActionLink>
                                {/* <img
                                    className="w-64"
                                    src={CDN_URL + order?.StlBite}
                                />
                                <img
                                    className="w-64"
                                    src={CDN_URL + order?.StlLower}
                                />
                                <img
                                    className="w-64"
                                    src={CDN_URL + order?.StlUpper}
                                /> */}
                            </div>
                        ) : (
                            <span className="flex gap-1 items-center text-lg">
                                <FaCheck />
                                Impression Gönder
                            </span>
                        )}
                    </div>
                    <hr />
                    <TreatmentPlansContainer
                        isRefinement={false}
                        order={order}
                    />
                </div>
            )}
        </>
    )
}

export default OrderDetails

// DELETE ORDER DIALOG
type DeleteOrderDialogProps = {
    order: {
        Name: string
        Id: number
    }
    isDeleteDialogOpen: boolean
    setIsDeleteDialogOpen: React.Dispatch<React.SetStateAction<boolean>>
}

const DeleteOrderDialog = ({
    order,
    isDeleteDialogOpen,
    setIsDeleteDialogOpen,
}: DeleteOrderDialogProps) => {
    const navigate = useNavigate()

    const deleteOrder = useCallback(async () => {
        const response = await apiDeleteOrder(order.Id)
        if ((response as any)?.data.Success == true) {
            toast.push('Vaka başarıyla silindi.')
            navigate('/orders')
        } else {
            toast.push('Vaka silinirken bir hata oluştu.')
        }
    }, [order, navigate])
    return (
        <ConfirmDialog
            confirmButtonColor="red"
            confirmText="Sil"
            cancelText="Vazgeç"
            type="danger"
            isOpen={isDeleteDialogOpen}
            title="Vakayı Sil"
            onClose={() => {
                setIsDeleteDialogOpen(false)
            }}
            onCancel={() => {
                setIsDeleteDialogOpen(false)
            }}
            onConfirm={deleteOrder}
        >
            {order.Id} numaralı vakayı silmek istediğinizden emin misiniz?
        </ConfirmDialog>
    )
}
